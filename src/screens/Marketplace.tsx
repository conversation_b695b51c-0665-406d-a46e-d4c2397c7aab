'use client';

import React, {useEffect, useState} from 'react';
import SharedLayout from '@/components/SharedLayout';
import { Wand2, Clock} from "lucide-react";
import {Card, CardContent} from "@/components/ui/card";
import {getStatusString, safeYMGoal} from "@/lib/utils";
import {Textarea} from "@/components/ui/textarea";
import {Checkbox} from "@/components/ui/checkbox";
import {Label} from "@/components/ui/label";
import {Button} from "@/components/ui/button";
import {usePBContext} from "@/context/PocketbaseContext";
import {Input} from "@/components/ui/input";
import {toast} from "@/hooks/use-toast";
import {useMutation, useQuery} from "@tanstack/react-query";
import Image from "next/image";
import WildberriesLogo from '@/components/logos/wildberries.svg'
import OzonLogo from '@/components/logos/ozon.svg'
import { useRouter } from 'next/navigation';
import {
  api,
  CreateImageRequest,
  CreateImageResponse,
  CreateVideoRequest,
  GenerationRequestData, GenerationRequestStatus,
  ImageModel,
  mutationKeys,
  TokenEstimationResponse,
  VideoModel
} from "@/lib/api";
import ActiveGeneration from "@/components/ActiveGeneration";

type MarketplaceTab = 'wildberries' | 'ozon';

export interface MarketplaceMode {
  id: string;
  name: string;
  description: string;
  example: string;
  requiresPrompt: boolean;
}

// Model data
const marketplaceModes: MarketplaceMode[] = [
  { id: 'background', name: 'Замена фона', description: 'Режим для изменения фона товара на фото', example: 'Товар на фоне арабских дюн в пустыне',requiresPrompt: true },
  { id: 'background-remove', name: 'Удалить фон', description: 'Режим для удаления фона товара на фото', example: '', requiresPrompt: false },
  { id: 'replace-object', name: 'Замена объекта', description: 'Режим для замены объекта на фото', example: 'Замени сапоги на туфли на высоком каблуке',requiresPrompt: true },
  { id: 'recolor', name: 'Перекраска', description: 'Режим для изменения цвета', example: 'Поменять цвет черного платья на красный', requiresPrompt: true},
  { id: 'embed-into-environment', name: 'Вписать в окружение', description: 'Позволяет вписать товар в обстановку или среду', example: 'Молодая женщина (белая кожа, 23 года, блондинка) на пляже пользуется кремом для загара', requiresPrompt: true},

  // ideas - style transfer,  upscale
  // { id: 'style-transfer', name: 'Перенос стиля', description: 'Режим для переноса стиля с одного объекта на другой', example: 'Сделать фото в стиле мультфильма', requiresPrompt: true},
  // { id: 'upscale', name: 'Улучшение качества', description: 'Режим для улучшения качества фото', example: '', requiresPrompt: false },
];

const createPromptAndTranslateForMode = (mode: MarketplaceMode) => {
  switch (mode.id) {
    case 'background':
      return {prompt:'Замена фона: ', translate: true};
    case 'background-remove':
      return {prompt:'Isolate the main subject on a seamless white background.', translate: false};
    case 'replace-object':
      return {prompt:'Замена объекта: ', translate: true};
    case 'recolor':
      return {prompt:'Поменять цвет: ', translate: true};
    case 'embed-into-environment':
      return {prompt:'Вписать в окружение: ', translate: true};
    default:
      return {prompt:'', translate: false};
  }
}

const modelDemoImages: Record<string, string> = {
  'background': '/model-demos/marketplace/background.jpg',
  'background-remove': '/model-demos/marketplace/background-remove.jpg',
  'replace-object': '/model-demos/marketplace/replace-object.jpg',
  'recolor': '/model-demos/marketplace/recolor.jpg',
  'embed-into-environment': '/model-demos/marketplace/embed-into-environment.jpg',
};

const Marketplace = () => {
  const { pb, user } = usePBContext();
  const router = useRouter();
  const [prompt, setPrompt] = useState('');
  const [enhancePrompt, setEnhancePrompt] = useState(false);
  const [activeTab, setActiveTab] = useState<MarketplaceTab>('wildberries');

  const [uploadedImage, setUploadedImage] = useState("");


  const [selectedMode, setSelectedMode] = useState(marketplaceModes[0]);

  // Token estimation state
  const [estimatedTokens, setEstimatedTokens] = useState<number>(0);


  // State for tracking active generation requests
  const [activeGeneration, setActiveGeneration] = useState<GenerationRequestData | undefined>(undefined);
  const uploadImageMutation = useMutation({
    mutationKey: mutationKeys.uploadImage,
    mutationFn: (file: File) => {
      safeYMGoal('upload_image');
      return api.uploadImage(pb, file);
    },
    onError: (error) => {
      console.error('Upload image error:', error);
      toast({
        title: 'Ошибка',
        description: 'Ошибка',
        variant: 'destructive',
      });
    },
  });


  const handleTokenEstimation = () => {
      const imageRequest: CreateImageRequest = {
        model: 'qwen-image-edit',
        prompt: "",
        image: uploadedImage,
        go_fast: true,
        output_format: 'jpg',
        output_quality: 90,
        disable_safety_checker: true,
      };
      console.log('Estimate image request: ', imageRequest)
      estimateImageTokens(imageRequest);
  };

  useEffect(() => {
    // run once
    handleTokenEstimation();
  }, []);



  const handleTabChange = (tab: MarketplaceTab) => {
    // safeYMGoal('tab_change_'+tab);
    setActiveTab(tab);
    // Set default mode for each tab
    // if (tab === 'video') {
    //   setSelectedModel('seedance-pro');
    // } else {
    //   setSelectedModel('imagen-4-ultra');
    // }
  };


  // Image token estimation mutation
  const {
    mutate: estimateImageTokens,
    isPending: isEstimatingImageTokens,
  } = useMutation({
    mutationKey: mutationKeys.estimateCreateImage,
    mutationFn: (request: CreateImageRequest) => api.estimateCreateImage(pb, request),
    onSuccess: (response: TokenEstimationResponse) => {
      setEstimatedTokens(response.estimation);
    },
    onError: (error) => {
      console.error('Image token estimation error:', error);
    },
  });

  // Image generation mutation
  const {
    mutate: generateImage,
    isPending: isGeneratingImage,
  } = useMutation({
    mutationKey: mutationKeys.createImage,
    mutationFn: (request: CreateImageRequest) => {
      safeYMGoal('generate_image');
      return api.createImage(pb, request);
    },
    onSuccess: (response: CreateImageResponse) => {
      // Add the new generation request to active generations
      const generationData: GenerationRequestData = {
        id: response.request_id,
        created: new Date().toISOString(),
        updated: new Date().toISOString(),
        user: '', // Will be filled by backend,
        error: '',
        model: '',
        type: 'image',
        replicate_id: response.prediction_id,
        status: response.status as GenerationRequestStatus,
        jsondata: {
          type: 'image',
          prompt: prompt,
          input: null,
          output: null
        },
        output_files: [],
        expand: null
      };

      setActiveGeneration(generationData);

      toast({
        title: 'Генерация изображения началась',
        description: 'Ваше изображение генерируется. Вы получите уведомление когда оно будет готово.',
        variant: 'default',
      });
    },
    onError: (error: Error) => {
      console.log('Generation start error', error);
      toast({
        title: 'Ошибка',
        description: 'Не удалось начать создать изображение. Попробуйте еще раз.',
        variant: 'destructive',
      });
    }
  });

  const handleGenerate = () => {
    const {prompt: defaultPrompt, translate} = createPromptAndTranslateForMode(selectedMode);
    const finalPrompt = selectedMode.requiresPrompt ? defaultPrompt + prompt : defaultPrompt;
    const imageRequest: CreateImageRequest = {
      model: 'qwen-image-edit',
      prompt:finalPrompt,
      translate_prompt: translate,
      enhance_prompt: enhancePrompt,
      image: uploadedImage,
      go_fast: true,
      output_format: 'jpg',
      output_quality: 95,
      disable_safety_checker: true,
    };
    console.log('Image request: ', imageRequest)
    generateImage(imageRequest);
  };


  const {data: longPolledActive} = useQuery({
    queryKey: ['longpoll-active',activeGeneration?.id],
    enabled: !!activeGeneration && (activeGeneration?.status != 'failed' && activeGeneration?.output_files.length <= 0),
    queryFn: () => api.getGenerationRequest(pb, activeGeneration.id),
    refetchInterval: 2000,
    staleTime: 0,
  })
  useEffect(() => {
    if (longPolledActive && longPolledActive.id == activeGeneration?.id) {
      setActiveGeneration({...longPolledActive});
      // If generation is completed or failed, show toast
      if (longPolledActive.status === 'completed') {
        toast({
          title: 'Успех',
          description: `${longPolledActive.type === 'video' ? 'Видео' : 'Изображение'} успешно создано!`,
          variant: 'default',
        });
      } else if (longPolledActive.status === 'failed') {
        toast({
          title: 'Ошибка',
          description: `Сгенерировать ${longPolledActive.type === 'video' ? 'видео' : 'изображение'} не удалось. Ошибка: ${longPolledActive.error}`,
          variant: 'destructive',
        });
      }
    }
  }, [longPolledActive]);

  return (
    <SharedLayout>
      <div className="space-y-8">
        <div className="space-y-6">
          {/* Header */}
          <div className="relative text-center">
            <h1 className="text-4xl font-bold text-gray-900 mb-3">Карточки для маркетплейсов</h1>
            <p className="text-gray-600 text-lg">Карточки и видео для ВБ (Wildberries), Ozon, Яндекс маркет</p>

            {/* History Button - positioned absolutely in top right */}
            <Button
              onClick={() => router.push('/history')}
              variant="outline"
              size="sm"
              className="absolute top-0 right-0 flex items-center gap-2 text-sm"
            >
              <Clock className="w-4 h-4" />
              <span className="hidden sm:inline">Все карточки</span>
              <span className="sm:hidden">История</span>
            </Button>
          </div>

          {/* Tab Selection */}
          {/*<div className="flex justify-center">*/}
          {/*  <div className="bg-gray-100 rounded-xl p-1 inline-flex">*/}
          {/*    <button*/}
          {/*      onClick={() => handleTabChange('wildberries')}*/}
          {/*      className={`px-6 py-2 rounded-lg transition-colors flex items-center space-x-2 ${*/}
          {/*        activeTab === 'wildberries'*/}
          {/*          ? 'bg-white text-gray-900 shadow-sm'*/}
          {/*          : 'text-gray-600 hover:text-gray-900'*/}
          {/*      }`}*/}
          {/*    >*/}
          {/*      <Image className="max-w-14 h-full" src={WildberriesLogo} alt={""}/>*/}
          {/*      <span>Wildberries</span>*/}
          {/*    </button>*/}
          {/*    <button*/}
          {/*      onClick={() => handleTabChange('ozon')}*/}
          {/*      className={`px-6 py-2 rounded-lg transition-colors flex items-center space-x-2 ${*/}
          {/*        activeTab === 'ozon'*/}
          {/*          ? 'bg-white text-gray-900 shadow-sm'*/}
          {/*          : 'text-gray-600 hover:text-gray-900'*/}
          {/*      }`}*/}
          {/*    >*/}
          {/*      /!*<Image className="w-4 h-4" />*!/*/}
          {/*      <Image className="max-w-14 h-full" src={OzonLogo} alt={""}/>*/}
          {/*      <span>Озон</span>*/}
          {/*    </button>*/}
          {/*  </div>*/}
          {/*</div>*/}

          {/* Generation Card */}
          <Card className="bg-white border border-gray-200 shadow-sm max-w-4xl mx-auto">
            <CardContent className="p-8">
              <div className="space-y-6">
                <div>
                  <label className="block text-gray-900 font-medium mb-3 text-lg">
                    Выбрать режим
                  </label>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    {marketplaceModes.map((mode: any) => (
                      <Card
                        key={mode.id}
                        className={`cursor-pointer transition-all overflow-hidden ${
                          selectedMode.id === mode.id
                            ? 'border-[var(--primary-blue)] bg-blue-50 ring-2 ring-blue-200'
                            : 'border-gray-200 hover:border-gray-300 hover:shadow-md'
                        }`}
                        onClick={() => {
                          setSelectedMode(mode);
                        }}
                      >
                        {/* Demo image for image models */}
                        {modelDemoImages[mode.id] && (
                          <div className="aspect-[4/3] overflow-hidden">
                            <img
                              src={modelDemoImages[mode.id]}
                              alt={`${mode.name} demo`}
                              className="w-full h-full object-cover"
                            />
                          </div>
                        )}
                        <CardContent className="p-4">
                          <div className="font-medium text-gray-900 mb-1">
                            {mode.name}
                          </div>
                          <div className="text-sm text-gray-600">
                            {mode.description}
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                </div>

                <div className="space-y-2">
                  <label className="block text-gray-900 font-medium mb-3 text-lg">
                    Загрузите изображение / фото товара
                  </label>
                  <Input
                    type="file"
                    accept='image/*'
                    onChange={async (e: React.ChangeEvent<HTMLInputElement>) => {
                      const file = e.target.files?.[0];
                      if (file) {
                        const res = await uploadImageMutation.mutateAsync(file)
                        if (res != '') {
                          setUploadedImage(res);
                        } else {
                          toast({
                            title: 'Ошибка',
                            description: 'Неизвестная ошибка загрузки изображения',
                            variant: 'destructive',
                          });
                        }
                      }
                    }}
                  />
                  {uploadedImage && (
                    <div className="text-sm text-muted-foreground">
                      Загружена
                    </div>
                  )}
                </div>
                {/* Prompt Input */}
                {selectedMode.requiresPrompt &&
                    <div>
                      <label className="block text-gray-900 font-medium mb-3 text-lg">
                        Опишите что вы хотите поменять или создать (на русском)
                      </label>
                      <Textarea
                          placeholder={selectedMode.example}
                          value={prompt}
                          onChange={(e) => setPrompt(e.target.value)}
                          className="min-h-[120px] border-gray-200 focus:border-[var(--primary-blue)] focus:ring-[var(--primary-blue)] text-base"
                      />


                      <div className="flex items-center space-x-2 mt-2">
                        <Checkbox
                            id="enhance-prompt"
                            checked={enhancePrompt}
                            onCheckedChange={(checked) => setEnhancePrompt(checked as boolean)}
                        />
                        <Label htmlFor="enhance-prompt" className="text-sm font-medium">
                          Улучшить запрос с помощью ИИ
                        </Label>
                      </div>
                    </div>
                }

                {/* Generate Button */}
                <Button
                  onClick={handleGenerate}
                  disabled={
                    (selectedMode.requiresPrompt && !prompt?.trim()) ||
                    isGeneratingImage ||
                    uploadImageMutation.isPending ||
                    (estimatedTokens > 0 && (user?.tokens || 0) < estimatedTokens)
                  }
                  className={`w-full py-4 text-xs font-bold text-wrap rounded-lg ${
                    estimatedTokens > 0 && (user?.tokens || 0) < estimatedTokens
                      ? 'bg-gray-400 hover:bg-gray-400 cursor-not-allowed text-white'
                      : 'bg-black hover:bg-gray-800 text-white'
                  }`}
                >
                  {(isGeneratingImage) ? (
                    <>
                      <div className="w-4 h-4 mr-2 border-2 border-white border-t-transparent rounded-full animate-spin" />
                      {`Генерируем ваше изображение... Это может занять несколько секунд.`}
                    </>
                  ) : estimatedTokens > 0 && (user?.tokens || 0) < estimatedTokens ? (
                    <>
                      <Wand2 className="w-5 h-5 mr-2" />
                      {`Недостаточно кредитов. Требуется: ${estimatedTokens} кредитов`}
                    </>
                  ) : (
                    <>
                      <Wand2 className="w-5 h-5 mr-2" />
                      <>
                        Генерировать изображение
                        {` ${estimatedTokens} кредитов`}
                      </>
                    </>
                  )}
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Full-Screen Modal */}
        <ActiveGeneration activeGeneration={activeGeneration} />
        </div>
    </SharedLayout>
  );
};

export default Marketplace;
